// Test file to verify long value support
public class TestLongValues {
    public static void main(String[] args) {
        // Test the problematic achievement threshold
        long milionario_a_vista_threshold = 450000000000L; // 450 billion
        
        System.out.println("milionario_a_vista threshold: " + milionario_a_vista_threshold);
        System.out.println("Integer.MAX_VALUE: " + Integer.MAX_VALUE);
        System.out.println("Threshold exceeds int max: " + (milionario_a_vista_threshold > Integer.MAX_VALUE));
        
        // Test other large thresholds
        long milionario_oficial_threshold = 1000000000000L; // 1 trillion
        System.out.println("milionario_oficial threshold: " + milionario_oficial_threshold);
        System.out.println("Threshold exceeds int max: " + (milionario_oficial_threshold > Integer.MAX_VALUE));
    }
}
