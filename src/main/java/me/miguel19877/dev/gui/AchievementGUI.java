package me.miguel19877.dev.gui;

import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.utils.Achievement;
import me.miguel19877.dev.utils.AchievementPlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Main achievements GUI showing categories
 */
public class AchievementGUI {
    
    private static final String GUI_TITLE = "§6§lConquistas";
    private static final int GUI_SIZE = 27; // 3 rows
    
    private final AchievementManager achievementManager = AchievementManager.getInstance();
    private final LanguageManager languageManager = LanguageManager.getInstance();
    
    /**
     * Open the main achievements GUI for a player
     */
    public void openMainGUI(Player player) {
        AchievementPlayerData playerData = achievementManager.getPlayerData(player.getUniqueId());
        if (playerData == null) {
            String message = languageManager.getMessage(player, "achievement.not_loaded", "");
            if (message == null) {
                message = "§cAchievement data not loaded yet. Please try again in a moment.";
            }
            player.sendMessage(message);
            return;
        }
        
        Inventory gui = Bukkit.createInventory(null, GUI_SIZE, GUI_TITLE);
        
        // Get achievement statistics by category
        Map<String, CategoryStats> categoryStats = getCategoryStats(player);
        
        // Bronze category (slot 10)
        gui.setItem(10, createCategoryHead("bronze", "Bronze", categoryStats.get("bronze")));
        
        // Silver category (slot 11)
        gui.setItem(11, createCategoryHead("silver", "Silver", categoryStats.get("silver")));
        
        // Gold category (slot 12)
        gui.setItem(12, createCategoryHead("gold", "Gold", categoryStats.get("gold")));
        
        // Platinum category (slot 13)
        gui.setItem(13, createCategoryHead("platinum", "Platinum", categoryStats.get("platinum")));
        
        // Mythic category (slot 14)
        gui.setItem(14, createCategoryHead("mythic", "Mythic", categoryStats.get("mythic")));
        
        // Fill empty slots with glass panes
        fillEmptySlots(gui);
        
        player.openInventory(gui);
    }
    
    /**
     * Create a player head for a category
     */
    private ItemStack createCategoryHead(String category, String displayName, CategoryStats stats) {
        ItemStack head = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta meta = (SkullMeta) head.getItemMeta();
        
        // Set display name with color based on category
        String colorCode = getCategoryColor(category);
        meta.setDisplayName(colorCode + "§l" + displayName.toUpperCase());
        
        // Create lore with statistics
        List<String> lore = new ArrayList<>();
        lore.add("§7Conquistas: §e" + stats.completed + "§7/§e" + stats.total);
        
        if (stats.total > 0) {
            double percentage = (double) stats.completed / stats.total * 100;
            lore.add("§7Progresso: §a" + String.format("%.1f", percentage) + "%");
        }
        
        lore.add("");
        lore.add("§eClique para ver as conquistas!");
        
        meta.setLore(lore);
        
        // Set skull texture based on category (you can customize these later)
        // Example: meta.setOwner("PlayerName"); for player skulls
        // Or use custom textures with:
        // PlayerProfile profile = Bukkit.createPlayerProfile(UUID.randomUUID());
        // profile.getTextures().setSkin(new URL("texture_url"));
        // meta.setOwnerProfile(profile);
        switch (category.toLowerCase()) {
            case "bronze":
                // You can set custom skull textures here later
                // For now, using default player head
                break;
            case "silver":
                break;
            case "gold":
                break;
            case "platinum":
                break;
            case "mythic":
                break;
        }
        
        head.setItemMeta(meta);
        return head;
    }
    
    /**
     * Get color code for category
     */
    private String getCategoryColor(String category) {
        switch (category.toLowerCase()) {
            case "bronze":
                return "§6"; // Gold/Orange
            case "silver":
                return "§7"; // Gray
            case "gold":
                return "§e"; // Yellow
            case "platinum":
                return "§b"; // Aqua
            case "mythic":
                return "§5"; // Purple
            default:
                return "§f"; // White
        }
    }
    
    /**
     * Get statistics for each category
     */
    private Map<String, CategoryStats> getCategoryStats(Player player) {
        Map<String, CategoryStats> stats = new java.util.HashMap<>();
        
        // Initialize category stats
        stats.put("bronze", new CategoryStats());
        stats.put("silver", new CategoryStats());
        stats.put("gold", new CategoryStats());
        stats.put("platinum", new CategoryStats());
        stats.put("mythic", new CategoryStats());
        
        AchievementPlayerData playerData = achievementManager.getPlayerData(player.getUniqueId());
        Map<String, Achievement> achievements = achievementManager.getAllAchievements();
        
        for (Achievement achievement : achievements.values()) {
            String category = achievement.getCategory() != null ? achievement.getCategory().toLowerCase() : "bronze";
            CategoryStats categoryStats = stats.get(category);
            
            if (categoryStats != null) {
                categoryStats.total++;
                if (playerData != null && playerData.isCompleted(achievement.getId())) {
                    categoryStats.completed++;
                }
            }
        }
        
        return stats;
    }
    
    /**
     * Fill empty slots with glass panes
     */
    private void fillEmptySlots(Inventory gui) {
        ItemStack glassPane = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = glassPane.getItemMeta();
        meta.setDisplayName(" ");
        glassPane.setItemMeta(meta);
        
        for (int i = 0; i < gui.getSize(); i++) {
            if (gui.getItem(i) == null) {
                gui.setItem(i, glassPane);
            }
        }
    }
    
    /**
     * Check if an inventory is the main achievements GUI
     */
    public static boolean isMainAchievementGUI(Inventory inventory) {
        return inventory.getSize() == GUI_SIZE && GUI_TITLE.equals(inventory.getTitle());
    }
    
    /**
     * Get category from clicked slot
     */
    public static String getCategoryFromSlot(int slot) {
        switch (slot) {
            case 10: return "bronze";
            case 11: return "silver";
            case 12: return "gold";
            case 13: return "platinum";
            case 14: return "mythic";
            default: return null;
        }
    }
    
    /**
     * Helper class for category statistics
     */
    private static class CategoryStats {
        int total = 0;
        int completed = 0;
    }
}
