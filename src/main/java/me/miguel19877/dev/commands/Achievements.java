package me.miguel19877.dev.commands;

import me.miguel19877.dev.gui.AchievementGUI;
import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Command to view achievements GUI
 */
public class Achievements implements CommandExecutor {

    private final AchievementGUI achievementGUI = new AchievementGUI();
    private final LanguageManager languageManager = LanguageManager.getInstance();
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cThis command can only be used by players.");
            return true;
        }

        Player player = (Player) sender;

        // Open the achievements GUI
        achievementGUI.openMainGUI(player);

        return true;
    }

}
