package me.miguel19877.dev.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Represents a player's achievement progress and completions
 */
public class AchievementPlayerData {
    private UUID playerId;
    private Map<String, Long> progress;
    private Map<String, Long> completions;

    public AchievementPlayerData() {
        // Default constructor for Gson
        this.progress = new HashMap<>();
        this.completions = new HashMap<>();
    }

    public AchievementPlayerData(UUID playerId) {
        this.playerId = playerId;
        this.progress = new HashMap<>();
        this.completions = new HashMap<>();
    }

    public AchievementPlayerData(UUID playerId, Map<String, Integer> progress, Map<String, Long> completions) {
        this.playerId = playerId;
        this.progress = progress != null ? progress : new HashMap<>();
        this.completions = completions != null ? completions : new HashMap<>();
    }

    // Get<PERSON> and Setters
    public UUID getPlayerId() {
        return playerId;
    }

    public void setPlayerId(UUID playerId) {
        this.playerId = playerId;
    }

    public Map<String, Integer> getProgress() {
        return progress;
    }

    public void setProgress(Map<String, Integer> progress) {
        this.progress = progress != null ? progress : new HashMap<>();
    }

    public Map<String, Long> getCompletions() {
        return completions;
    }

    public void setCompletions(Map<String, Long> completions) {
        this.completions = completions != null ? completions : new HashMap<>();
    }

    // Utility methods
    public int getProgress(String achievementId) {
        return progress.getOrDefault(achievementId, 0);
    }

    public void setProgress(String achievementId, int progressValue) {
        progress.put(achievementId, progressValue);
    }

    public boolean isCompleted(String achievementId) {
        return completions.containsKey(achievementId);
    }

    public Long getCompletionTime(String achievementId) {
        return completions.get(achievementId);
    }

    public void setCompleted(String achievementId, long completionTime) {
        completions.put(achievementId, completionTime);
    }

    public void incrementProgress(String achievementId, int amount) {
        int currentProgress = getProgress(achievementId);
        setProgress(achievementId, currentProgress + amount);
    }

    @Override
    public String toString() {
        return "AchievementPlayerData{" +
                "playerId=" + playerId +
                ", progress=" + progress +
                ", completions=" + completions +
                '}';
    }
}
