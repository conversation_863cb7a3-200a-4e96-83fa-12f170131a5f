package me.miguel19877.dev.managers;

import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.AchievementHelper;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manages playtime tracking for achievements
 */
public class PlaytimeManager {
    
    private static PlaytimeManager instance;
    private final Map<UUID, Long> sessionStartTimes = new HashMap<>();
    private final Map<UUID, Long> totalPlaytime = new HashMap<>(); // in seconds
    
    private PlaytimeManager() {
        startPlaytimeTracking();
    }
    
    public static PlaytimeManager getInstance() {
        if (instance == null) {
            instance = new PlaytimeManager();
        }
        return instance;
    }
    
    /**
     * Start tracking playtime for a player
     */
    public void startTracking(Player player) {
        UUID playerId = player.getUniqueId();
        sessionStartTimes.put(playerId, System.currentTimeMillis());
        
        // Initialize total playtime if not exists
        if (!totalPlaytime.containsKey(playerId)) {
            totalPlaytime.put(playerId, 0L);
        }
    }
    
    /**
     * Stop tracking playtime for a player and save session time
     */
    public void stopTracking(Player player) {
        UUID playerId = player.getUniqueId();
        Long startTime = sessionStartTimes.remove(playerId);
        
        if (startTime != null) {
            long sessionTime = (System.currentTimeMillis() - startTime) / 1000; // Convert to seconds
            long currentTotal = totalPlaytime.getOrDefault(playerId, 0L);
            long newTotal = currentTotal + sessionTime;
            totalPlaytime.put(playerId, newTotal);
            
            // Check achievements
            checkPlaytimeAchievements(player, newTotal);
        }
    }
    
    /**
     * Get total playtime for a player in seconds
     */
    public long getTotalPlaytime(UUID playerId) {
        return totalPlaytime.getOrDefault(playerId, 0L);
    }
    
    /**
     * Update playtime and check achievements for a player
     */
    public void updatePlaytime(Player player) {
        UUID playerId = player.getUniqueId();
        Long startTime = sessionStartTimes.get(playerId);
        
        if (startTime != null) {
            long currentSessionTime = (System.currentTimeMillis() - startTime) / 1000;
            long currentTotal = totalPlaytime.getOrDefault(playerId, 0L);
            long totalWithCurrentSession = currentTotal + currentSessionTime;
            
            checkPlaytimeAchievements(player, totalWithCurrentSession);
        }
    }
    
    /**
     * Check playtime-based achievements
     * Always updates progress to show in GUI, regardless of completion status
     */
    private void checkPlaytimeAchievements(Player player, long totalSeconds) {
        // Always update progress for all playtime achievements so they show in GUI

        // inicio_de_jornada - 1 hour (3600 seconds)
        AchievementHelper.setProgress(player, "inicio_de_jornada", (int) totalSeconds);

        // capataz_da_mina - 12 hours (43200 seconds)
        AchievementHelper.setProgress(player, "capataz_da_mina", (int) totalSeconds);

        // veterano_ativo - 50 hours (180000 seconds)
        AchievementHelper.setProgress(player, "veterano_ativo", (int) totalSeconds);

        // tempo_e_ouro - 150 hours (540000 seconds)
        AchievementHelper.setProgress(player, "tempo_e_ouro", (int) totalSeconds);

        // tempo_e_poder - 500 hours (1800000 seconds)
        AchievementHelper.setProgress(player, "tempo_e_poder", (int) totalSeconds);
    }
    
    /**
     * Start the periodic playtime tracking task
     */
    private void startPlaytimeTracking() {
        new BukkitRunnable() {
            @Override
            public void run() {
                // Update playtime for all online players every minute
                for (Player player : Bukkit.getOnlinePlayers()) {
                    updatePlaytime(player);
                }
            }
        }.runTaskTimerAsynchronously(Rankup.getInstance(), 0L, 1200L); // Every minute (1200 ticks)
    }
    
    /**
     * Clean up player data when they leave
     */
    public void cleanupPlayer(UUID playerId) {
        sessionStartTimes.remove(playerId);
        // Keep totalPlaytime for when they return
    }
    
    /**
     * Load playtime data for a player (if you have persistent storage)
     * For now, this starts fresh each server restart
     */
    public void loadPlayerData(Player player) {
        // In a full implementation, you would load from database/Redis here
        // For now, we start tracking from 0 each server restart
        startTracking(player);
    }
    
    /**
     * Save playtime data for a player (if you have persistent storage)
     */
    public void savePlayerData(Player player) {
        // In a full implementation, you would save to database/Redis here
        stopTracking(player);
    }
}
